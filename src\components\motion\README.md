# Lazy-Loaded Framer Motion Components

This directory contains lazy-loaded Framer Motion components designed to improve initial bundle size and performance by loading motion animations only when needed.

## Overview

The lazy motion system provides:
- **Dynamic imports**: Framer Motion is loaded only when motion components are used
- **Suspense boundaries**: Smooth loading states while motion components load
- **Performance optimization**: Reduced initial bundle size
- **Backward compatibility**: Drop-in replacement for direct Framer Motion usage

## Components

### MotionProvider
Provides motion context and handles dynamic loading of Framer Motion library.

```tsx
import { MotionProvider } from '@/components/motion/MotionProvider';

function App() {
  return (
    <MotionProvider>
      {/* Your app content */}
    </MotionProvider>
  );
}
```

### LazyMotion
Main component for lazy-loaded motion animations.

```tsx
import { LazyMotion, motionVariants } from '@/components/motion/MotionProvider';

function MyComponent() {
  return (
    <LazyMotion fallback={<div>Loading animation...</div>}>
      {(motion) => (
        <motion.div
          variants={motionVariants.fadeInUp}
          initial="hidden"
          animate="visible"
        >
          Content with animation
        </motion.div>
      )}
    </LazyMotion>
  );
}
```

### LazyAnimatePresence
Lazy-loaded AnimatePresence for exit animations.

```tsx
import { LazyAnimatePresence } from '@/components/motion/MotionProvider';

function MyComponent() {
  return (
    <LazyAnimatePresence mode="wait">
      {showContent && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          Content
        </motion.div>
      )}
    </LazyAnimatePresence>
  );
}
```

## Pre-defined Motion Variants

The system includes common animation variants:

```tsx
import { motionVariants } from '@/components/motion/MotionProvider';

// Available variants:
motionVariants.fadeInUp
motionVariants.fadeIn
motionVariants.staggerContainer
motionVariants.scaleOnHover
motionVariants.slideInLeft
motionVariants.slideInRight
motionVariants.cardHover
motionVariants.iconHover
```

## Hooks

### useMotionAnimation
Lazy-loaded useAnimation hook.

```tsx
import { useMotionAnimation } from '@/components/motion/MotionProvider';

function MyComponent() {
  const controls = useMotionAnimation();
  
  const handleClick = () => {
    controls.start({ scale: 1.2 });
  };
  
  return <button onClick={handleClick}>Animate</button>;
}
```

### useMotionInView
Lazy-loaded useInView hook.

```tsx
import { useMotionInView } from '@/components/motion/MotionProvider';

function MyComponent() {
  const { ref, inView } = useMotionInView({ threshold: 0.1 });
  
  return (
    <div ref={ref}>
      {inView ? 'In view!' : 'Not in view'}
    </div>
  );
}
```

## Suspense Boundaries

Enhanced suspense boundaries for motion components:

```tsx
import { MotionSuspenseBoundary } from '@/components/SuspenseBoundary';

function MyComponent() {
  return (
    <MotionSuspenseBoundary>
      <LazyMotion>
        {(motion) => (
          <motion.div>Animated content</motion.div>
        )}
      </LazyMotion>
    </MotionSuspenseBoundary>
  );
}
```

## Migration Guide

### Before (Direct Framer Motion)
```tsx
import { motion } from 'framer-motion';

function MyComponent() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      Content
    </motion.div>
  );
}
```

### After (Lazy Motion)
```tsx
import { LazyMotion, motionVariants } from '@/components/motion/MotionProvider';

function MyComponent() {
  return (
    <LazyMotion>
      {(motion) => (
        <motion.div
          variants={motionVariants.fadeInUp}
          initial="hidden"
          animate="visible"
        >
          Content
        </motion.div>
      )}
    </LazyMotion>
  );
}
```

## Performance Benefits

- **Initial bundle reduction**: ~116KB (38KB gzipped) moved to lazy chunks
- **Faster initial load**: Critical path doesn't include motion library
- **Better caching**: Motion library cached separately
- **Progressive enhancement**: Pages load without waiting for animations

## Best Practices

1. **Use MotionSuspenseBoundary** for sections with multiple motion components
2. **Prefer pre-defined variants** over custom animations for consistency
3. **Provide meaningful fallbacks** for loading states
4. **Test without JavaScript** to ensure graceful degradation
5. **Monitor bundle sizes** to ensure chunks remain optimal

## Bundle Analysis

After implementation:
- Main bundle: Reduced by ~116KB
- Motion chunk: Lazy-loaded when needed
- Page chunks: Separate chunks for each page
- Better cache efficiency and loading performance
