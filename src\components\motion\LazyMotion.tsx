import React, { Suspense, lazy, ComponentProps } from 'react';
import { SkeletonLoader } from '@/components/SuspenseBoundary';

// Lazy load Framer Motion components
const MotionDiv = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.div 
  }))
);

const MotionSection = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.section 
  }))
);

const MotionSpan = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.span 
  }))
);

const MotionH1 = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.h1 
  }))
);

const MotionH2 = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.h2 
  }))
);

const MotionH3 = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.h3 
  }))
);

const MotionP = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.p 
  }))
);

const MotionButton = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.button 
  }))
);

const MotionImg = lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.img 
  }))
);

// Fallback component for motion elements
const MotionFallback: React.FC<{ 
  className?: string; 
  children?: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
}> = ({ 
  className = "", 
  children, 
  as: Component = 'div' 
}) => (
  <Component className={`opacity-0 animate-pulse ${className}`}>
    {children || <SkeletonLoader className="h-8 w-full" />}
  </Component>
);

// Wrapper components with Suspense
export const LazyMotionDiv: React.FC<ComponentProps<typeof MotionDiv>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="div">{props.children}</MotionFallback>}>
    <MotionDiv {...props} />
  </Suspense>
);

export const LazyMotionSection: React.FC<ComponentProps<typeof MotionSection>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="section">{props.children}</MotionFallback>}>
    <MotionSection {...props} />
  </Suspense>
);

export const LazyMotionSpan: React.FC<ComponentProps<typeof MotionSpan>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="span">{props.children}</MotionFallback>}>
    <MotionSpan {...props} />
  </Suspense>
);

export const LazyMotionH1: React.FC<ComponentProps<typeof MotionH1>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="h1">{props.children}</MotionFallback>}>
    <MotionH1 {...props} />
  </Suspense>
);

export const LazyMotionH2: React.FC<ComponentProps<typeof MotionH2>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="h2">{props.children}</MotionFallback>}>
    <MotionH2 {...props} />
  </Suspense>
);

export const LazyMotionH3: React.FC<ComponentProps<typeof MotionH3>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="h3">{props.children}</MotionFallback>}>
    <MotionH3 {...props} />
  </Suspense>
);

export const LazyMotionP: React.FC<ComponentProps<typeof MotionP>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="p">{props.children}</MotionFallback>}>
    <MotionP {...props} />
  </Suspense>
);

export const LazyMotionButton: React.FC<ComponentProps<typeof MotionButton>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="button">{props.children}</MotionFallback>}>
    <MotionButton {...props} />
  </Suspense>
);

export const LazyMotionImg: React.FC<ComponentProps<typeof MotionImg>> = (props) => (
  <Suspense fallback={<MotionFallback className={props.className} as="img">{props.children}</MotionFallback>}>
    <MotionImg {...props} />
  </Suspense>
);

// Export a hook for accessing motion utilities
export const useMotionUtils = () => {
  return React.useMemo(() => ({
    // Common animation variants
    fadeInUp: {
      hidden: { opacity: 0, y: 20 },
      visible: { 
        opacity: 1, 
        y: 0,
        transition: { duration: 0.6, ease: 'easeOut' }
      }
    },
    fadeIn: {
      hidden: { opacity: 0 },
      visible: { 
        opacity: 1,
        transition: { duration: 0.6 }
      }
    },
    staggerContainer: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.2
        }
      }
    },
    scaleOnHover: {
      hover: { 
        scale: 1.05,
        transition: { duration: 0.3, ease: 'easeOut' }
      }
    },
    slideInLeft: {
      hidden: { opacity: 0, x: -20 },
      visible: { 
        opacity: 1, 
        x: 0,
        transition: { duration: 0.6, ease: 'easeOut' }
      }
    }
  }), []);
};

// Higher-order component for lazy motion
export const withLazyMotion = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return React.memo((props: P) => (
    <Suspense fallback={<MotionFallback />}>
      <Component {...props} />
    </Suspense>
  ));
};

export default {
  LazyMotionDiv,
  LazyMotionSection,
  LazyMotionSpan,
  LazyMotionH1,
  LazyMotionH2,
  LazyMotionH3,
  LazyMotionP,
  LazyMotionButton,
  LazyMotionImg,
  useMotionUtils,
  withLazyMotion
};
