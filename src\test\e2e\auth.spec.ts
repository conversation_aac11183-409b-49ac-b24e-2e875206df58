import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/')
  })

  test('should navigate to sign in page', async ({ page }) => {
    // Click the Sign In button in navigation
    await page.click('text=Sign In')
    
    // Should be on the sign in page
    await expect(page).toHaveURL('/signin')
    
    // Should see the sign in form
    await expect(page.locator('h1')).toContainText('Metamorphic Labs')
    await expect(page.locator('h2')).toContainText('Welcome back')
    
    // Should see email input
    await expect(page.locator('input[type="email"]')).toBeVisible()
    
    // Should see magic link button
    await expect(page.locator('button:has-text("Send magic link")')).toBeVisible()
    
    // Should see social auth buttons
    await expect(page.locator('button:has-text("Google")')).toBeVisible()
    await expect(page.locator('button:has-text("GitHub")')).toBeVisible()
    await expect(page.locator('button:has-text("Discord")')).toBeVisible()
  })

  test('should navigate to sign up page', async ({ page }) => {
    // Click the Get Started button in navigation
    await page.click('text=Get Started')
    
    // Should be on the sign up page
    await expect(page).toHaveURL('/signup')
    
    // Should see the sign up form
    await expect(page.locator('h1')).toContainText('Metamorphic Labs')
    await expect(page.locator('h2')).toContainText('Create your account')
    
    // Should see email input
    await expect(page.locator('input[type="email"]')).toBeVisible()
    
    // Should see magic link button
    await expect(page.locator('button:has-text("Send magic link")')).toBeVisible()
    
    // Should see social auth buttons
    await expect(page.locator('button:has-text("Google")')).toBeVisible()
    await expect(page.locator('button:has-text("GitHub")')).toBeVisible()
    await expect(page.locator('button:has-text("Discord")')).toBeVisible()
    
    // Should see benefits section
    await expect(page.locator('text=What you\'ll get:')).toBeVisible()
  })

  test('should switch between sign in and sign up', async ({ page }) => {
    // Go to sign in page
    await page.goto('/signin')
    
    // Click sign up link
    await page.click('text=Sign up')
    await expect(page).toHaveURL('/signup')
    
    // Click sign in link
    await page.click('text=Sign in')
    await expect(page).toHaveURL('/signin')
  })

  test('should validate email input on sign in', async ({ page }) => {
    await page.goto('/signin')
    
    // Try to submit without email
    await page.click('button:has-text("Send magic link")')
    
    // Should show validation error
    await expect(page.locator('text=Email is required')).toBeVisible()
    
    // Enter invalid email
    await page.fill('input[type="email"]', 'invalid-email')
    await page.click('button:has-text("Send magic link")')
    
    // Should show validation error
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible()
  })

  test('should validate email input on sign up', async ({ page }) => {
    await page.goto('/signup')
    
    // Try to submit without email
    await page.click('button:has-text("Send magic link")')
    
    // Should show validation error
    await expect(page.locator('text=Email is required')).toBeVisible()
    
    // Enter invalid email
    await page.fill('input[type="email"]', 'invalid-email')
    await page.click('button:has-text("Send magic link")')
    
    // Should show validation error
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible()
  })

  test('should show loading state during magic link submission', async ({ page }) => {
    await page.goto('/signin')
    
    // Fill valid email
    await page.fill('input[type="email"]', '<EMAIL>')
    
    // Click submit button
    await page.click('button:has-text("Send magic link")')
    
    // Should show loading state (this will fail without proper Supabase setup)
    // In a real test environment, you'd mock the Supabase response
    await expect(page.locator('button:has-text("Sending magic link...")')).toBeVisible()
  })

  test('should have accessible form elements', async ({ page }) => {
    await page.goto('/signin')
    
    // Check for proper labels
    await expect(page.locator('label[for="email"]')).toBeVisible()
    
    // Check for proper ARIA attributes
    const emailInput = page.locator('input[type="email"]')
    await expect(emailInput).toHaveAttribute('id', 'email')
    await expect(emailInput).toHaveAttribute('autocomplete', 'email')
    
    // Check button accessibility
    const submitButton = page.locator('button:has-text("Send magic link")')
    await expect(submitButton).toHaveAttribute('type', 'submit')
  })

  test('should navigate back to home from auth pages', async ({ page }) => {
    await page.goto('/signin')
    
    // Click back to home link
    await page.click('text=Back to home')
    
    // Should be on home page
    await expect(page).toHaveURL('/')
  })

  test('should handle auth callback page', async ({ page }) => {
    // Navigate to auth callback page
    await page.goto('/auth/callback')
    
    // Should show loading state initially
    await expect(page.locator('text=Completing sign in...')).toBeVisible()
    
    // Should show loading spinner
    await expect(page.locator('[data-testid="loading-spinner"]').or(page.locator('.animate-spin'))).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/signin')
    
    // Form should be visible and properly sized
    await expect(page.locator('input[type="email"]')).toBeVisible()
    await expect(page.locator('button:has-text("Send magic link")')).toBeVisible()
    
    // Social buttons should stack properly
    const socialButtons = page.locator('button:has-text("Google"), button:has-text("GitHub"), button:has-text("Discord")')
    await expect(socialButtons.first()).toBeVisible()
  })
})
