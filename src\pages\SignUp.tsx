import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { AuthForm } from '@/components/auth/AuthForm'
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth'
import { SEOHead } from '@/components/SEOHead'
import { PerformanceMonitor } from '@/components/PerformanceMonitor'
import { SectionSuspenseBoundary } from '@/components/SuspenseBoundary'

const SignUp: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated, isLoading } = useSupabaseAuth()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/', { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate])

  const handleSignUpSuccess = () => {
    // Navigation will be handled by the auth state change
    // The useEffect above will redirect once isAuthenticated becomes true
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  return (
    <PerformanceMonitor id="SignUp">
      <SEOHead
        title="Sign Up - Metamorphic Labs"
        description="Create your Metamorphic Labs account to access cutting-edge AI systems, quantum computing tools, and intelligent software solutions."
        keywords="sign up, register, create account, Metamorphic Labs, AI, quantum computing"
        canonicalUrl="/signup"
      />

      <div className="min-h-screen bg-black text-white">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-blue-900/20 to-black pointer-events-none" />
        
        {/* Content */}
        <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-12">
          <SectionSuspenseBoundary>
            <div className="w-full max-w-md">
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  Metamorphic Labs
                </h1>
                <p className="text-gray-400 mt-2">
                  Join the Future of AI & Quantum Computing
                </p>
              </div>

              {/* Auth Form */}
              <AuthForm
                mode="signup"
                onSuccess={handleSignUpSuccess}
                showBackButton={true}
              />

              {/* Additional signup benefits */}
              <div className="mt-8 p-4 bg-gray-900/50 rounded-lg border border-gray-800">
                <h3 className="text-sm font-semibold text-white mb-2">
                  What you'll get:
                </h3>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Access to Catalyst AI platform</li>
                  <li>• Metamorphic Reactor tools</li>
                  <li>• Vault 024 NFT ecosystem</li>
                  <li>• Priority support & updates</li>
                </ul>
              </div>
            </div>
          </SectionSuspenseBoundary>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 left-1/3 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 right-1/3 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-full blur-3xl" />
        </div>
      </div>
    </PerformanceMonitor>
  )
}

export default SignUp
