import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth'
import { Github, Chrome, MessageSquare } from 'lucide-react'

interface SocialAuthButtonsProps {
  mode?: 'signin' | 'signup'
  className?: string
  disabled?: boolean
}

export const SocialAuthButtons: React.FC<SocialAuthButtonsProps> = ({
  mode = 'signin',
  className = '',
  disabled = false,
}) => {
  const { signInWithOAuth, isSigningIn } = useSupabaseAuth()

  const handleOAuthSignIn = async (provider: 'google' | 'github' | 'discord') => {
    await signInWithOAuth(provider)
  }

  const isLoading = isSigningIn || disabled

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-gray-300 dark:border-gray-600" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-white dark:bg-black px-2 text-gray-500 dark:text-gray-400">
            Or {mode === 'signin' ? 'sign in' : 'sign up'} with
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
        <Button
          type="button"
          variant="outline"
          onClick={() => handleOAuthSignIn('google')}
          disabled={isLoading}
          className="w-full"
        >
          <Chrome className="mr-2 h-4 w-4" />
          Google
        </Button>

        <Button
          type="button"
          variant="outline"
          onClick={() => handleOAuthSignIn('github')}
          disabled={isLoading}
          className="w-full"
        >
          <Github className="mr-2 h-4 w-4" />
          GitHub
        </Button>

        <Button
          type="button"
          variant="outline"
          onClick={() => handleOAuthSignIn('discord')}
          disabled={isLoading}
          className="w-full"
        >
          <MessageSquare className="mr-2 h-4 w-4" />
          Discord
        </Button>
      </div>
    </div>
  )
}
