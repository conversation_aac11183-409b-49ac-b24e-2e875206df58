name: Security Audit

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run security audit daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  security-audit:
    name: NPM Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run security audit
        run: npm audit --production
        
      - name: Run audit with JSON output for reporting
        run: npm audit --production --json > audit-results.json
        continue-on-error: true
        
      - name: Upload audit results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-audit-results
          path: audit-results.json
          retention-days: 30
          
      - name: Check for high/critical vulnerabilities
        run: |
          HIGH_CRITICAL=$(npm audit --production --json | jq '.metadata.vulnerabilities.high + .metadata.vulnerabilities.critical')
          echo "High/Critical vulnerabilities found: $HIGH_CRITICAL"
          if [ "$HIGH_CRITICAL" -gt 0 ]; then
            echo "❌ High or critical vulnerabilities detected!"
            echo "Please run 'npm audit fix' to resolve security issues."
            exit 1
          else
            echo "✅ No high or critical vulnerabilities found."
          fi
