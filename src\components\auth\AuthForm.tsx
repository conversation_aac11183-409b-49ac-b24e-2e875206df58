import React from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MagicLinkForm } from './MagicLinkForm'
import { SocialAuthButtons } from './SocialAuthButtons'
import { Link } from 'react-router-dom'
import { ArrowLeft } from 'lucide-react'

interface AuthFormProps {
  mode: 'signin' | 'signup'
  className?: string
  showBackButton?: boolean
  onSuccess?: () => void
}

export const AuthForm: React.FC<AuthFormProps> = ({
  mode,
  className = '',
  showBackButton = true,
  onSuccess,
}) => {
  const isSignIn = mode === 'signin'

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      {showBackButton && (
        <Link
          to="/"
          className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white mb-6 transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to home
        </Link>
      )}

      <Card className="border-gray-200 dark:border-gray-800">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">
            {isSignIn ? 'Welcome back' : 'Create your account'}
          </CardTitle>
          <CardDescription>
            {isSignIn
              ? 'Sign in to your account to continue'
              : 'Get started with your free account today'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Magic Link Form */}
          <MagicLinkForm mode={mode} onSuccess={onSuccess} />

          {/* Social Auth Buttons */}
          <SocialAuthButtons mode={mode} />

          {/* Footer Links */}
          <div className="text-center text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              {isSignIn ? "Don't have an account?" : 'Already have an account?'}
            </span>{' '}
            <Link
              to={isSignIn ? '/signup' : '/signin'}
              className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              {isSignIn ? 'Sign up' : 'Sign in'}
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Terms and Privacy */}
      <p className="mt-6 text-xs text-center text-gray-600 dark:text-gray-400">
        By {isSignIn ? 'signing in' : 'creating an account'}, you agree to our{' '}
        <Link
          to="/terms"
          className="underline hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          Terms of Service
        </Link>{' '}
        and{' '}
        <Link
          to="/privacy"
          className="underline hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          Privacy Policy
        </Link>
        .
      </p>
    </div>
  )
}
