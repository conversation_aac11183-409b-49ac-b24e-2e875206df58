import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { supabase, type User, type Session, type AuthError } from '@/lib/supabase'

// Authentication state interface
interface AuthState {
  // Session and user state
  user: User | null
  session: Session | null
  isAuthenticated: boolean
  
  // Loading states
  isLoading: boolean
  isSigningIn: boolean
  isSigningUp: boolean
  isSigningOut: boolean
  
  // Error state
  error: AuthError | null
  
  // Actions
  setSession: (session: Session | null) => void
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: AuthError | null) => void
  
  // Auth methods
  signInWithMagicLink: (email: string, redirectTo?: string) => Promise<{ error: AuthError | null }>
  signInWithOAuth: (provider: 'google' | 'github' | 'discord') => Promise<{ error: AuthError | null }>
  signUp: (email: string, password?: string, redirectTo?: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  
  // Utility methods
  clearError: () => void
  initialize: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        session: null,
        isAuthenticated: false,
        isLoading: false,
        isSigningIn: false,
        isSigningUp: false,
        isSigningOut: false,
        error: null,

        // State setters
        setSession: (session) => {
          set({
            session,
            user: session?.user || null,
            isAuthenticated: !!session,
          })
        },

        setUser: (user) => {
          set({ user, isAuthenticated: !!user })
        },

        setLoading: (loading) => {
          set({ isLoading: loading })
        },

        setError: (error) => {
          set({ error })
        },

        // Clear error state
        clearError: () => {
          set({ error: null })
        },

        // Sign in with magic link
        signInWithMagicLink: async (email, redirectTo) => {
          set({ isSigningIn: true, error: null })
          
          try {
            const { error } = await supabase.auth.signInWithOtp({
              email,
              options: {
                emailRedirectTo: redirectTo || `${window.location.origin}/auth/callback`,
                shouldCreateUser: true,
              },
            })

            if (error) {
              set({ error, isSigningIn: false })
              return { error }
            }

            set({ isSigningIn: false })
            return { error: null }
          } catch (err) {
            const error = err as AuthError
            set({ error, isSigningIn: false })
            return { error }
          }
        },

        // Sign in with OAuth provider
        signInWithOAuth: async (provider) => {
          set({ isSigningIn: true, error: null })
          
          try {
            const { error } = await supabase.auth.signInWithOAuth({
              provider,
              options: {
                redirectTo: `${window.location.origin}/auth/callback`,
              },
            })

            if (error) {
              set({ error, isSigningIn: false })
              return { error }
            }

            // OAuth redirect will handle the rest
            return { error: null }
          } catch (err) {
            const error = err as AuthError
            set({ error, isSigningIn: false })
            return { error }
          }
        },

        // Sign up (magic link or password)
        signUp: async (email, password, redirectTo) => {
          set({ isSigningUp: true, error: null })
          
          try {
            let result
            
            if (password) {
              // Sign up with email and password
              result = await supabase.auth.signUp({
                email,
                password,
                options: {
                  emailRedirectTo: redirectTo || `${window.location.origin}/auth/callback`,
                },
              })
            } else {
              // Sign up with magic link
              result = await supabase.auth.signInWithOtp({
                email,
                options: {
                  emailRedirectTo: redirectTo || `${window.location.origin}/auth/callback`,
                  shouldCreateUser: true,
                },
              })
            }

            const { error } = result

            if (error) {
              set({ error, isSigningUp: false })
              return { error }
            }

            set({ isSigningUp: false })
            return { error: null }
          } catch (err) {
            const error = err as AuthError
            set({ error, isSigningUp: false })
            return { error }
          }
        },

        // Sign out
        signOut: async () => {
          set({ isSigningOut: true, error: null })
          
          try {
            const { error } = await supabase.auth.signOut()

            if (error) {
              set({ error, isSigningOut: false })
              return { error }
            }

            set({
              user: null,
              session: null,
              isAuthenticated: false,
              isSigningOut: false,
            })

            return { error: null }
          } catch (err) {
            const error = err as AuthError
            set({ error, isSigningOut: false })
            return { error }
          }
        },

        // Initialize auth state
        initialize: async () => {
          set({ isLoading: true })
          
          try {
            // Get initial session
            const { data: { session }, error } = await supabase.auth.getSession()
            
            if (error) {
              console.error('Error getting session:', error)
              set({ error, isLoading: false })
              return
            }

            // Set initial session
            get().setSession(session)

            // Listen for auth changes
            supabase.auth.onAuthStateChange((event, session) => {
              console.log('Auth state changed:', event, session)
              get().setSession(session)
            })

            set({ isLoading: false })
          } catch (err) {
            console.error('Error initializing auth:', err)
            set({ error: err as AuthError, isLoading: false })
          }
        },
      }),
      {
        name: 'auth-storage',
        // Only persist non-sensitive data
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
)
