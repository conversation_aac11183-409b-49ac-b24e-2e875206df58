import { test, expect } from '@playwright/test';

test.describe('Homepage Smoke Test', () => {
  test('should load the root route successfully', async ({ page }) => {
    // Navigate to the root route
    await page.goto('/');

    // Basic smoke test checks
    await expect(page).toHaveTitle(/Metamorphic Labs/);
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('h1')).toBeVisible();

    // Verify page is interactive (no critical errors)
    await expect(page.locator('body')).toBeVisible();

    // Check that main content area exists
    await expect(page.locator('main')).toBeVisible();
  });
});
