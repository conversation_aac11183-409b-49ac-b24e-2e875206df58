import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// React Compiler plugin (experimental)
const reactCompilerPlugin = () => {
  return {
    name: 'react-compiler',
    transform(code: string, id: string) {
      // Only process React components
      if (id.includes('node_modules') || !id.match(/\.(jsx?|tsx?)$/)) {
        return null;
      }

      // Add React Compiler transformations here when available
      // This is a placeholder for future React Compiler integration
      return null;
    },
  };
};

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react({
      // Enable React 19 features
      jsxRuntime: 'automatic',
      jsxImportSource: 'react',
      // Enable development optimizations
      devTarget: 'es2022',
      // Enable React Compiler integration (experimental)
      plugins: mode === 'development' ? [] : [],
    }),
    mode === 'development' && componentTagger(),
    // Add React Compiler plugin (experimental)
    mode === 'production' && reactCompilerPlugin(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core React libraries
          if (id.includes('react') || id.includes('react-dom')) {
            return 'vendor';
          }

          // Framer Motion - lazy loaded
          if (id.includes('framer-motion')) {
            return 'motion';
          }

          // UI libraries
          if (id.includes('@radix-ui') || id.includes('lucide-react')) {
            return 'ui';
          }

          // Router
          if (id.includes('react-router')) {
            return 'router';
          }

          // Utilities
          if (id.includes('class-variance-authority') ||
              id.includes('clsx') ||
              id.includes('tailwind-merge') ||
              id.includes('@tanstack/react-query')) {
            return 'utils';
          }

          // Analytics and monitoring
          if (id.includes('@vercel/analytics') ||
              id.includes('@vercel/speed-insights') ||
              id.includes('react-helmet-async')) {
            return 'analytics';
          }

          // Page components - separate chunks for each page
          if (id.includes('/pages/Index')) {
            return 'page-index';
          }
          if (id.includes('/pages/About')) {
            return 'page-about';
          }
          if (id.includes('/pages/Systems')) {
            return 'page-systems';
          }
          if (id.includes('/pages/Contact')) {
            return 'page-contact';
          }
          if (id.includes('/pages/Projects')) {
            return 'page-projects';
          }
          if (id.includes('/pages/')) {
            return 'pages-other';
          }

          // Motion components
          if (id.includes('/components/motion/')) {
            return 'motion-components';
          }

          // Node modules default
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
        },
      },
    },
    chunkSizeWarningLimit: 1000,
    // Enable code splitting
    target: 'es2020',
    minify: 'esbuild', // Use esbuild for faster builds
    sourcemap: false,
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      'lucide-react'
    ],
    // Exclude framer-motion from pre-bundling since it's lazy loaded
    exclude: ['framer-motion'],
  },
  // Enable experimental features for better performance
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `/${filename}` };
      }
      return { relative: true };
    },
  },
}));
