import React, { Suspense, lazy, createContext, useContext, ReactNode } from 'react';
import { SkeletonLoader } from '@/components/SuspenseBoundary';

// Lazy load the entire Framer Motion library
const FramerMotion = lazy(() => import('framer-motion'));

// Create context for motion components
interface MotionContextType {
  isLoaded: boolean;
  motion?: typeof import('framer-motion').motion;
  AnimatePresence?: typeof import('framer-motion').AnimatePresence;
  useAnimation?: typeof import('framer-motion').useAnimation;
  useInView?: typeof import('framer-motion').useInView;
  useScroll?: typeof import('framer-motion').useScroll;
  useTransform?: typeof import('framer-motion').useTransform;
  useSpring?: typeof import('framer-motion').useSpring;
}

const MotionContext = createContext<MotionContextType>({ isLoaded: false });

// Motion provider component
export const MotionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [motionLib, setMotionLib] = React.useState<MotionContextType>({ isLoaded: false });

  React.useEffect(() => {
    // Dynamically import Framer Motion when needed
    import('framer-motion').then((lib) => {
      setMotionLib({
        isLoaded: true,
        motion: lib.motion,
        AnimatePresence: lib.AnimatePresence,
        useAnimation: lib.useAnimation,
        useInView: lib.useInView,
        useScroll: lib.useScroll,
        useTransform: lib.useTransform,
        useSpring: lib.useSpring,
      });
    });
  }, []);

  return (
    <MotionContext.Provider value={motionLib}>
      {children}
    </MotionContext.Provider>
  );
};

// Hook to use motion context
export const useMotion = () => {
  const context = useContext(MotionContext);
  if (!context) {
    throw new Error('useMotion must be used within a MotionProvider');
  }
  return context;
};

// Lazy motion component wrapper
interface LazyMotionProps {
  children: (motion: typeof import('framer-motion').motion) => ReactNode;
  fallback?: ReactNode;
  className?: string;
}

export const LazyMotion: React.FC<LazyMotionProps> = ({ 
  children, 
  fallback,
  className = ""
}) => {
  const { isLoaded, motion } = useMotion();

  if (!isLoaded || !motion) {
    return (
      <div className={`opacity-0 animate-pulse ${className}`}>
        {fallback || <SkeletonLoader className="h-8 w-full" />}
      </div>
    );
  }

  return <>{children(motion)}</>;
};

// Lazy AnimatePresence wrapper
interface LazyAnimatePresenceProps {
  children: ReactNode;
  mode?: 'wait' | 'sync' | 'popLayout';
  initial?: boolean;
  onExitComplete?: () => void;
}

export const LazyAnimatePresence: React.FC<LazyAnimatePresenceProps> = (props) => {
  const { isLoaded, AnimatePresence } = useMotion();

  if (!isLoaded || !AnimatePresence) {
    return <>{props.children}</>;
  }

  return <AnimatePresence {...props} />;
};

// Common motion variants
export const motionVariants = {
  fadeInUp: {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  },
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.6 }
    }
  },
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  },
  scaleOnHover: {
    hover: { 
      scale: 1.05,
      transition: { duration: 0.3, ease: 'easeOut' }
    }
  },
  slideInLeft: {
    hidden: { opacity: 0, x: -20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  },
  slideInRight: {
    hidden: { opacity: 0, x: 20 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.6, ease: 'easeOut' }
    }
  },
  cardHover: {
    hover: { 
      y: -8,
      transition: { duration: 0.3, ease: 'easeOut' }
    }
  },
  iconHover: {
    hover: { 
      scale: 1.1, 
      rotate: 5,
      transition: { duration: 0.3 }
    }
  }
};

// Hook for motion animations
export const useMotionAnimation = () => {
  const { isLoaded, useAnimation } = useMotion();
  
  return React.useMemo(() => {
    if (!isLoaded || !useAnimation) {
      return {
        controls: null,
        animate: () => {},
        set: () => {},
        start: () => Promise.resolve(),
        stop: () => {},
        mount: () => {},
        unmount: () => {}
      };
    }
    
    return useAnimation();
  }, [isLoaded, useAnimation]);
};

// Hook for in-view animations
export const useMotionInView = (options?: Parameters<typeof import('framer-motion').useInView>[1]) => {
  const { isLoaded, useInView } = useMotion();
  const ref = React.useRef(null);
  
  const inView = React.useMemo(() => {
    if (!isLoaded || !useInView) {
      return false;
    }
    
    return useInView(ref, options);
  }, [isLoaded, useInView, options]);
  
  return { ref, inView };
};

// Hook for scroll animations
export const useMotionScroll = () => {
  const { isLoaded, useScroll } = useMotion();
  
  return React.useMemo(() => {
    if (!isLoaded || !useScroll) {
      return {
        scrollX: { get: () => 0 },
        scrollY: { get: () => 0 },
        scrollXProgress: { get: () => 0 },
        scrollYProgress: { get: () => 0 }
      };
    }
    
    return useScroll();
  }, [isLoaded, useScroll]);
};

export default MotionProvider;
