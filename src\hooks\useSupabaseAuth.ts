import { useEffect, useCallback } from 'react'
import { useAuthStore } from '@/store/useAuthStore'
import { supabase, type User, type Session, type AuthError } from '@/lib/supabase'
import { toast } from '@/hooks/use-toast'

// Hook return type
interface UseSupabaseAuthReturn {
  // State
  user: User | null
  session: Session | null
  isAuthenticated: boolean
  isLoading: boolean
  isSigningIn: boolean
  isSigningUp: boolean
  isSigningOut: boolean
  error: AuthError | null

  // Methods
  signInWithMagicLink: (email: string, options?: { redirectTo?: string; showToast?: boolean }) => Promise<boolean>
  signInWithOAuth: (provider: 'google' | 'github' | 'discord', options?: { redirectTo?: string }) => Promise<boolean>
  signUp: (email: string, options?: { password?: string; redirectTo?: string; showToast?: boolean }) => Promise<boolean>
  signOut: (options?: { showToast?: boolean }) => Promise<boolean>
  clearError: () => void
  refreshSession: () => Promise<void>
}

/**
 * Custom hook for Supabase authentication
 * Provides a clean interface for authentication operations with automatic error handling and toast notifications
 */
export const useSupabaseAuth = (): UseSupabaseAuthReturn => {
  const {
    user,
    session,
    isAuthenticated,
    isLoading,
    isSigningIn,
    isSigningUp,
    isSigningOut,
    error,
    signInWithMagicLink: storeSignInWithMagicLink,
    signInWithOAuth: storeSignInWithOAuth,
    signUp: storeSignUp,
    signOut: storeSignOut,
    clearError,
    initialize,
  } = useAuthStore()

  // Initialize auth on mount
  useEffect(() => {
    initialize()
  }, [initialize])

  // Enhanced sign in with magic link
  const signInWithMagicLink = useCallback(
    async (email: string, options: { redirectTo?: string; showToast?: boolean } = {}) => {
      const { showToast: shouldShowToast = true, redirectTo } = options

      const { error } = await storeSignInWithMagicLink(email, redirectTo)

      if (error) {
        if (shouldShowToast) {
          toast({
            title: 'Sign In Failed',
            description: error.message || 'Failed to send magic link. Please try again.',
            variant: 'destructive',
          })
        }
        return false
      }

      if (shouldShowToast) {
        toast({
          title: 'Magic Link Sent',
          description: 'Check your email for the sign-in link!',
          variant: 'default',
        })
      }

      return true
    },
    [storeSignInWithMagicLink]
  )

  // Enhanced OAuth sign in
  const signInWithOAuth = useCallback(
    async (provider: 'google' | 'github' | 'discord', options: { redirectTo?: string } = {}) => {
      const { redirectTo } = options

      const { error } = await storeSignInWithOAuth(provider)

      if (error) {
        toast({
          title: 'Sign In Failed',
          description: error.message || `Failed to sign in with ${provider}. Please try again.`,
          variant: 'destructive',
        })
        return false
      }

      // OAuth will redirect, so we don't show a success toast here
      return true
    },
    [storeSignInWithOAuth]
  )

  // Enhanced sign up
  const signUp = useCallback(
    async (
      email: string,
      options: { password?: string; redirectTo?: string; showToast?: boolean } = {}
    ) => {
      const { password, redirectTo, showToast: shouldShowToast = true } = options

      const { error } = await storeSignUp(email, password, redirectTo)

      if (error) {
        if (shouldShowToast) {
          toast({
            title: 'Sign Up Failed',
            description: error.message || 'Failed to create account. Please try again.',
            variant: 'destructive',
          })
        }
        return false
      }

      if (shouldShowToast) {
        if (password) {
          toast({
            title: 'Account Created',
            description: 'Please check your email to verify your account.',
            variant: 'default',
          })
        } else {
          toast({
            title: 'Magic Link Sent',
            description: 'Check your email for the sign-up link!',
            variant: 'default',
          })
        }
      }

      return true
    },
    [storeSignUp]
  )

  // Enhanced sign out
  const signOut = useCallback(
    async (options: { showToast?: boolean } = {}) => {
      const { showToast: shouldShowToast = true } = options

      const { error } = await storeSignOut()

      if (error) {
        if (shouldShowToast) {
          toast({
            title: 'Sign Out Failed',
            description: error.message || 'Failed to sign out. Please try again.',
            variant: 'destructive',
          })
        }
        return false
      }

      if (shouldShowToast) {
        toast({
          title: 'Signed Out',
          description: 'You have been successfully signed out.',
          variant: 'default',
        })
      }

      return true
    },
    [storeSignOut]
  )

  // Refresh session
  const refreshSession = useCallback(async () => {
    try {
      const { error } = await supabase.auth.refreshSession()
      if (error) {
        console.error('Error refreshing session:', error)
        toast({
          title: 'Session Refresh Failed',
          description: 'Please sign in again.',
          variant: 'destructive',
        })
      }
    } catch (err) {
      console.error('Error refreshing session:', err)
    }
  }, [])

  return {
    // State
    user,
    session,
    isAuthenticated,
    isLoading,
    isSigningIn,
    isSigningUp,
    isSigningOut,
    error,

    // Methods
    signInWithMagicLink,
    signInWithOAuth,
    signUp,
    signOut,
    clearError,
    refreshSession,
  }
}
