
import React, { useMemo } from 'react';
import { LazyMotion, motionVariants } from '@/components/motion/MotionProvider';
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button-gradient";
import { Card, CardContent } from "@/components/ui/card";
import { ProjectCard } from "@/components/ProjectCard";
import { SEOHead } from "@/components/SEOHead";
import { SectionSuspenseBoundary, MotionSuspenseBoundary } from "@/components/SuspenseBoundary";
import { useDeferredSearch, useConcurrentAnimation } from "@/hooks/useConcurrentFeatures";
import { ArrowRight, Zap, Brain, Palette, Code, Server, Database, Sparkles } from "lucide-react";

const Index = () => {
  const { isPending, animateWithTransition } = useConcurrentAnimation();

  // Memoize project data for deferred rendering
  const projectsData = useMemo(() => [
    {
      title: "Catalyst",
      description: "Advanced prompt engineering and AI model optimization platform with multi-model chaining capabilities and intelligent optimization algorithms.",
      url: "https://catalyst.metamorphiclabs.ai",
      status: "active" as const,
      type: "catalyst" as const,
      features: ["Multi-Model Orchestration", "Prompt Engineering", "Performance Analytics"]
    },
    {
      title: "Metamorphic Reactor",
      description: "Multi-agent orchestration platform serving as the central nervous system for complex AI workflows and automated decision-making processes.",
      status: "development" as const,
      type: "reactor" as const,
      features: ["Multi-Agent Coordination", "Workflow Automation", "Real-time Monitoring"]
    },
    {
      title: "Vault 024",
      description: "Decentralized generative art gallery and NFT ecosystem featuring exclusive collections, artist collaborations, and blockchain-verified authenticity.",
      url: "https://vault024.metamorphiclabs.ai",
      status: "active" as const,
      type: "vault024" as const,
      features: ["AI Art Generation", "NFT Marketplace", "Blockchain Verification"]
    }
  ], []);

  const { deferredValue: deferredProjects } = useDeferredSearch(projectsData);

  return (
    <>
      <SEOHead
        url="https://metamorphiclabs.ai/"
      />
      <div className="flex flex-col bg-black">
        {/* Hero Section */}
      <section className="relative min-h-[90vh] flex items-center justify-center bg-gradient-hero">
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-black/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="animate-fade-in">
                <div className="w-48 h-48 mx-auto mb-8 flex items-center justify-center animate-pulse">
                  <img
                    src="/metamorphic-labs-logo.png"
                    alt="Metamorphic Labs"
                    className="w-48 h-48 object-contain opacity-50"
                  />
                </div>
              </div>
            }>
              {(motion) => (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, ease: 'easeOut' }}
                >
                  {/* Animated Logo */}
                  <motion.div
                    className="w-48 h-48 mx-auto mb-8 flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    animate={{
                      filter: [
                        'drop-shadow(0 0 20px rgba(59, 130, 246, 0.5))',
                        'drop-shadow(0 0 30px rgba(147, 51, 234, 0.7))',
                        'drop-shadow(0 0 20px rgba(59, 130, 246, 0.5))'
                      ]
                    }}
                    transition={{
                      filter: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
                    }}
                  >
                    <img
                      src="/metamorphic-labs-logo.png"
                      alt="Metamorphic Labs"
                      className="w-48 h-48 object-contain drop-shadow-[0_0_15px_rgba(59,130,246,0.4)]"
                    />
                  </motion.div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-white">
              Redefining Reality with{" "}
              <span className="text-gradient">AI, Quantum Systems</span>
              <br />
              & Intelligent Software
            </h1>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed"
                  >
                    Metamorphic Labs pioneers next-generation AI systems, multi-agent orchestration,
                    generative creativity, and boundary-pushing software solutions. From deep prompt
                    ecosystems to decentralized art galleries—we build the future.
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    className="flex flex-col sm:flex-row gap-4 justify-center"
                  >
                    <Button variant="gradient" size="xl" asChild>
                      <a href="https://catalyst.metamorphiclabs.ai" target="_blank" rel="noopener noreferrer">
                        Explore Catalyst <ArrowRight className="ml-2 h-5 w-5" />
                      </a>
                    </Button>
                    <Button variant="gradient-outline" size="xl" asChild>
                      <a href="#metamorphic-reactor">
                        Learn About Metamorphic Reactor
                      </a>
                    </Button>
                    <Button variant="vault" size="xl" asChild>
                      <a href="https://vault024.metamorphiclabs.ai" target="_blank" rel="noopener noreferrer">
                        Visit Vault 024
                      </a>
                    </Button>
                  </motion.div>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="text-center mb-16 animate-pulse">
                <div className="h-8 bg-gray-700 rounded w-64 mx-auto mb-4"></div>
                <div className="h-4 bg-gray-700 rounded w-96 mx-auto"></div>
              </div>
            }>
              {(motion) => (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                    Our <span className="text-gradient">Ecosystem</span>
                  </h2>
                  <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                    Four core pillars that form the foundation of our intelligent systems and creative platforms
                  </p>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>

          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <Card className="bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700">
                      <CardContent className="p-6 text-center">
                        <div className="w-16 h-16 bg-gray-700 rounded-xl mx-auto mb-4"></div>
                        <div className="h-4 bg-gray-700 rounded w-3/4 mx-auto mb-3"></div>
                        <div className="h-3 bg-gray-700 rounded w-full"></div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            }>
              {(motion) => (
                <motion.div
                  variants={motionVariants.staggerContainer}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
                >
                  <motion.div variants={motionVariants.fadeInUp}>
                    <Card className="group hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:border-primary/50 hover:glow-primary">
                      <CardContent className="p-6 text-center">
                        <motion.div
                          className="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mb-4 mx-auto"
                          whileHover={motionVariants.iconHover.hover}
                        >
                          <Brain className="h-8 w-8 text-white" />
                        </motion.div>
                        <h3 className="text-xl font-semibold mb-3 text-white">Metamorphic AI Ecosystem</h3>
                        <p className="text-gray-300 text-sm">
                          Comprehensive AI research and development platform with adaptive workflows and intelligent optimization
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={motionVariants.fadeInUp}>
                    <Card className="group hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:border-secondary/50 hover:glow-secondary">
                      <CardContent className="p-6 text-center">
                        <motion.div
                          className="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mb-4 mx-auto"
                          whileHover={motionVariants.iconHover.hover}
                        >
                          <Server className="h-8 w-8 text-white" />
                        </motion.div>
                        <h3 className="text-xl font-semibold mb-3 text-white">Multi-AI Orchestration</h3>
                        <p className="text-gray-300 text-sm">
                          Seamless coordination between multiple AI agents through the Metamorphic Reactor platform
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={motionVariants.fadeInUp}>
                    <Card className="vault-024-card group hover:shadow-2xl transition-all duration-300 hover:glow-gold">
                      <CardContent className="p-6 text-center">
                        <motion.div
                          className="w-16 h-16 bg-gold/20 border-2 border-gold rounded-xl flex items-center justify-center mb-4 mx-auto"
                          whileHover={motionVariants.iconHover.hover}
                        >
                          <Palette className="h-8 w-8 text-gold" />
                        </motion.div>
                        <h3 className="text-xl font-semibold mb-3 vault-024-text">Generative Art + NFTs</h3>
                        <p className="text-gold/80 text-sm">
                          Exclusive AI-generated art collections and decentralized gallery through Vault 024
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div variants={motionVariants.fadeInUp}>
                    <Card className="group hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700 hover:border-accent/50 hover:glow-primary">
                      <CardContent className="p-6 text-center">
                        <motion.div
                          className="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mb-4 mx-auto"
                          whileHover={motionVariants.iconHover.hover}
                        >
                          <Sparkles className="h-8 w-8 text-white" />
                        </motion.div>
                        <h3 className="text-xl font-semibold mb-3 text-white">Prompt Engineering</h3>
                        <p className="text-gray-300 text-sm">
                          Advanced prompt optimization and model fine-tuning through the Catalyst platform
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>
        </div>
      </section>

      {/* Projects Preview */}
      <section id="metamorphic-reactor" className="py-20 bg-gradient-to-br from-black via-slate-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="text-center mb-16 animate-pulse">
                <div className="h-8 bg-gray-700 rounded w-64 mx-auto mb-4"></div>
                <div className="h-4 bg-gray-700 rounded w-96 mx-auto"></div>
              </div>
            }>
              {(motion) => (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="text-center mb-16"
                >
                  <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                    Our <span className="text-gradient">Systems</span>
                  </h2>
                  <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                    Explore our comprehensive ecosystem of AI platforms and intelligent environments
                  </p>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>

          <SectionSuspenseBoundary skeletonCount={3}>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {deferredProjects.map((project, index) => (
                <ProjectCard
                  key={project.title}
                  title={project.title}
                  description={project.description}
                  url={project.url}
                  status={project.status}
                  type={project.type}
                  features={project.features}
                />
              ))}
            </div>
          </SectionSuspenseBoundary>

          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="text-center mt-12 animate-pulse">
                <div className="h-10 bg-gray-700 rounded w-48 mx-auto"></div>
              </div>
            }>
              {(motion) => (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  viewport={{ once: true }}
                  className="text-center mt-12"
                >
                  <Button variant="gradient" size="lg" asChild>
                    <Link to="/systems">
                      Explore All Systems <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <MotionSuspenseBoundary>
            <LazyMotion fallback={
              <div className="animate-pulse">
                <div className="h-8 bg-white/20 rounded w-64 mx-auto mb-4"></div>
                <div className="h-4 bg-white/20 rounded w-96 mx-auto mb-8"></div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <div className="h-10 bg-white/20 rounded w-32"></div>
                  <div className="h-10 bg-white/20 rounded w-40"></div>
                </div>
              </div>
            }>
              {(motion) => (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                    Ready to Build the Future?
                  </h2>
                  <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                    Join us in redefining reality through AI, quantum systems, and intelligent software.
                    Let's create something extraordinary together.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button variant="vault" size="lg" asChild>
                      <Link to="/contact">Start Your Project</Link>
                    </Button>
                    <Button variant="gradient-outline" size="lg" asChild>
                      <Link to="/about">Learn More About Us</Link>
                    </Button>
                  </div>
                </motion.div>
              )}
            </LazyMotion>
          </MotionSuspenseBoundary>
        </div>
      </section>
      </div>
    </>
  );
};

export default Index;
