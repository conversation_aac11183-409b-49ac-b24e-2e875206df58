import { renderHook, act } from '@testing-library/react'
import { useSupabaseAuth } from '../useSupabaseAuth'
import { useAuthStore } from '@/store/useAuthStore'

// Mock the auth store
jest.mock('@/store/useAuthStore')
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  toast: jest.fn(),
}))

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      refreshSession: jest.fn(),
    },
  },
}))

describe('useSupabaseAuth', () => {
  const mockAuthStore = {
    user: null,
    session: null,
    isAuthenticated: false,
    isLoading: false,
    isSigningIn: false,
    isSigningUp: false,
    isSigningOut: false,
    error: null,
    signInWithMagicLink: jest.fn(),
    signInWithOAuth: jest.fn(),
    signUp: jest.fn(),
    signOut: jest.fn(),
    clearError: jest.fn(),
    initialize: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseAuthStore.mockReturnValue(mockAuthStore)
  })

  it('should initialize auth on mount', () => {
    renderHook(() => useSupabaseAuth())
    
    expect(mockAuthStore.initialize).toHaveBeenCalledTimes(1)
  })

  it('should return auth state from store', () => {
    const { result } = renderHook(() => useSupabaseAuth())
    
    expect(result.current.user).toBe(mockAuthStore.user)
    expect(result.current.session).toBe(mockAuthStore.session)
    expect(result.current.isAuthenticated).toBe(mockAuthStore.isAuthenticated)
    expect(result.current.isLoading).toBe(mockAuthStore.isLoading)
  })

  it('should handle successful magic link sign in', async () => {
    mockAuthStore.signInWithMagicLink.mockResolvedValue({ error: null })
    
    const { result } = renderHook(() => useSupabaseAuth())
    
    let success: boolean
    await act(async () => {
      success = await result.current.signInWithMagicLink('<EMAIL>')
    })
    
    expect(success!).toBe(true)
    expect(mockAuthStore.signInWithMagicLink).toHaveBeenCalledWith(
      '<EMAIL>',
      undefined
    )
  })

  it('should handle failed magic link sign in', async () => {
    const error = { message: 'Invalid email' }
    mockAuthStore.signInWithMagicLink.mockResolvedValue({ error })
    
    const { result } = renderHook(() => useSupabaseAuth())
    
    let success: boolean
    await act(async () => {
      success = await result.current.signInWithMagicLink('invalid-email')
    })
    
    expect(success!).toBe(false)
  })

  it('should handle OAuth sign in', async () => {
    mockAuthStore.signInWithOAuth.mockResolvedValue({ error: null })
    
    const { result } = renderHook(() => useSupabaseAuth())
    
    let success: boolean
    await act(async () => {
      success = await result.current.signInWithOAuth('google')
    })
    
    expect(success!).toBe(true)
    expect(mockAuthStore.signInWithOAuth).toHaveBeenCalledWith('google')
  })

  it('should handle sign up', async () => {
    mockAuthStore.signUp.mockResolvedValue({ error: null })
    
    const { result } = renderHook(() => useSupabaseAuth())
    
    let success: boolean
    await act(async () => {
      success = await result.current.signUp('<EMAIL>')
    })
    
    expect(success!).toBe(true)
    expect(mockAuthStore.signUp).toHaveBeenCalledWith(
      '<EMAIL>',
      undefined,
      undefined
    )
  })

  it('should handle sign out', async () => {
    mockAuthStore.signOut.mockResolvedValue({ error: null })
    
    const { result } = renderHook(() => useSupabaseAuth())
    
    let success: boolean
    await act(async () => {
      success = await result.current.signOut()
    })
    
    expect(success!).toBe(true)
    expect(mockAuthStore.signOut).toHaveBeenCalled()
  })

  it('should clear errors', () => {
    const { result } = renderHook(() => useSupabaseAuth())
    
    act(() => {
      result.current.clearError()
    })
    
    expect(mockAuthStore.clearError).toHaveBeenCalled()
  })
})
