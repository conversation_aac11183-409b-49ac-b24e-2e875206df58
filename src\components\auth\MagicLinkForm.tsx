import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth'
import { Mail, Loader2 } from 'lucide-react'

// Form validation schema
const magicLinkSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
})

type MagicLinkFormData = z.infer<typeof magicLinkSchema>

interface MagicLinkFormProps {
  mode?: 'signin' | 'signup'
  className?: string
  onSuccess?: () => void
}

export const MagicLinkForm: React.FC<MagicLinkFormProps> = ({
  mode = 'signin',
  className = '',
  onSuccess,
}) => {
  const { signInWithMagicLink, signUp, isSigningIn, isSigningUp } = useSupabaseAuth()
  const [isSubmitted, setIsSubmitted] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm<MagicLinkFormData>({
    resolver: zodResolver(magicLinkSchema),
    mode: 'onChange',
  })

  const isLoading = isSigningIn || isSigningUp

  const onSubmit = async (data: MagicLinkFormData) => {
    try {
      let success = false

      if (mode === 'signin') {
        success = await signInWithMagicLink(data.email)
      } else {
        success = await signUp(data.email) // Magic link signup
      }

      if (success) {
        setIsSubmitted(true)
        reset()
        onSuccess?.()
      }
    } catch (error) {
      console.error('Magic link error:', error)
    }
  }

  if (isSubmitted) {
    return (
      <div className={`text-center space-y-4 ${className}`}>
        <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
          <Mail className="w-6 h-6 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Check your email
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            We've sent a magic link to your email address. Click the link to{' '}
            {mode === 'signin' ? 'sign in' : 'complete your account setup'}.
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsSubmitted(false)}
          className="w-full"
        >
          Send another link
        </Button>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium">
          Email address
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          autoComplete="email"
          disabled={isLoading}
          {...register('email')}
          className={errors.email ? 'border-red-500 focus:border-red-500' : ''}
        />
        {errors.email && (
          <p className="text-sm text-red-600 dark:text-red-400">
            {errors.email.message}
          </p>
        )}
      </div>

      <Button
        type="submit"
        disabled={!isValid || isLoading}
        className="w-full"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Sending magic link...
          </>
        ) : (
          <>
            <Mail className="mr-2 h-4 w-4" />
            Send magic link
          </>
        )}
      </Button>

      <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
        {mode === 'signin' 
          ? "We'll send you a secure link to sign in instantly."
          : "We'll send you a secure link to create your account."
        }
      </p>
    </form>
  )
}
