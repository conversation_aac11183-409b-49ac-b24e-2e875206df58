import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { supabase } from '@/lib/supabase'
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth'
import { SEOHead } from '@/components/SEOHead'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

const AuthCallback: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { isAuthenticated } = useSupabaseAuth()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState<string>('')

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Handle OAuth callback
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          setErrorMessage(error.message)
          setStatus('error')
          return
        }

        // Check for error in URL params
        const errorParam = searchParams.get('error')
        const errorDescription = searchParams.get('error_description')
        
        if (errorParam) {
          console.error('OAuth error:', errorParam, errorDescription)
          setErrorMessage(errorDescription || errorParam)
          setStatus('error')
          return
        }

        // Check if we have a session
        if (data.session) {
          setStatus('success')
          // Redirect after a brief success message
          setTimeout(() => {
            navigate('/', { replace: true })
          }, 2000)
        } else {
          // No session but no error - might be email confirmation
          const type = searchParams.get('type')
          if (type === 'signup') {
            setStatus('success')
            setTimeout(() => {
              navigate('/signin', { replace: true })
            }, 3000)
          } else {
            setErrorMessage('No session found. Please try signing in again.')
            setStatus('error')
          }
        }
      } catch (err) {
        console.error('Unexpected error in auth callback:', err)
        setErrorMessage('An unexpected error occurred. Please try again.')
        setStatus('error')
      }
    }

    handleAuthCallback()
  }, [navigate, searchParams])

  // If already authenticated, redirect immediately
  useEffect(() => {
    if (isAuthenticated && status !== 'loading') {
      navigate('/', { replace: true })
    }
  }, [isAuthenticated, navigate, status])

  const handleRetry = () => {
    navigate('/signin', { replace: true })
  }

  return (
    <>
      <SEOHead
        title="Authentication - Metamorphic Labs"
        description="Completing your authentication to Metamorphic Labs."
        noIndex={true}
      />

      <div className="min-h-screen bg-black text-white flex items-center justify-center px-4">
        <div className="text-center max-w-md mx-auto">
          {status === 'loading' && (
            <>
              <Loader2 className="w-12 h-12 animate-spin text-blue-400 mx-auto mb-4" />
              <h1 className="text-xl font-semibold mb-2">Completing sign in...</h1>
              <p className="text-gray-400">Please wait while we verify your authentication.</p>
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <h1 className="text-xl font-semibold mb-2">Authentication successful!</h1>
              <p className="text-gray-400">Redirecting you to the dashboard...</p>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h1 className="text-xl font-semibold mb-2">Authentication failed</h1>
              <p className="text-gray-400 mb-6">
                {errorMessage || 'Something went wrong during authentication.'}
              </p>
              <button
                onClick={handleRetry}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                Try again
              </button>
            </>
          )}
        </div>
      </div>
    </>
  )
}

export default AuthCallback
