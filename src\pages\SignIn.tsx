import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { AuthForm } from '@/components/auth/AuthForm'
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth'
import { SEOHead } from '@/components/SEOHead'
import { PerformanceMonitor } from '@/components/PerformanceMonitor'
import { SectionSuspenseBoundary } from '@/components/SuspenseBoundary'

const SignIn: React.FC = () => {
  const navigate = useNavigate()
  const { isAuthenticated, isLoading } = useSupabaseAuth()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/', { replace: true })
    }
  }, [isAuthenticated, isLoading, navigate])

  const handleSignInSuccess = () => {
    // Navigation will be handled by the auth state change
    // The useEffect above will redirect once isAuthenticated becomes true
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if authenticated (will redirect)
  if (isAuthenticated) {
    return null
  }

  return (
    <PerformanceMonitor id="SignIn">
      <SEOHead
        title="Sign In - Metamorphic Labs"
        description="Sign in to your Metamorphic Labs account to access our AI systems, quantum computing initiatives, and intelligent software solutions."
        keywords="sign in, login, authentication, Metamorphic Labs, AI, quantum computing"
        canonicalUrl="/signin"
      />

      <div className="min-h-screen bg-black text-white">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-black pointer-events-none" />
        
        {/* Content */}
        <div className="relative z-10 min-h-screen flex items-center justify-center px-4 py-12">
          <SectionSuspenseBoundary>
            <div className="w-full max-w-md">
              {/* Header */}
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Metamorphic Labs
                </h1>
                <p className="text-gray-400 mt-2">
                  Advanced AI & Quantum Computing
                </p>
              </div>

              {/* Auth Form */}
              <AuthForm
                mode="signin"
                onSuccess={handleSignInSuccess}
                showBackButton={true}
              />
            </div>
          </SectionSuspenseBoundary>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl" />
        </div>
      </div>
    </PerformanceMonitor>
  )
}

export default SignIn
