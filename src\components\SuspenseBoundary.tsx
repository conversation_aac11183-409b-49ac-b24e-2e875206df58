import React, { Suspense, ReactNode } from 'react';
import { Loader2, Z<PERSON>, <PERSON>, Code } from 'lucide-react';

interface SuspenseBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
}

/**
 * Loading component with smooth animations (no motion dependency)
 */
const DefaultFallback = () => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="flex flex-col items-center space-y-4 animate-fade-in">
      <div className="animate-spin">
        <Loader2 className="h-8 w-8 text-primary" />
      </div>
      <p className="text-sm text-gray-400 animate-pulse">
        Loading...
      </p>
    </div>
  </div>
);

/**
 * Enhanced loading component for page-level lazy loading
 */
const PageLoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center bg-black">
    <div className="flex flex-col items-center space-y-6 animate-fade-in">
      {/* Animated logo placeholder */}
      <div className="w-24 h-24 bg-gradient-primary rounded-full flex items-center justify-center animate-pulse">
        <Zap className="h-12 w-12 text-white" />
      </div>

      {/* Loading text with gradient */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gradient">
          Metamorphic Labs
        </h2>
        <p className="text-gray-400 animate-pulse">
          Loading experience...
        </p>
      </div>

      {/* Progress indicator */}
      <div className="w-64 h-1 bg-gray-800 rounded-full overflow-hidden">
        <div className="h-full bg-gradient-primary animate-pulse rounded-full"></div>
      </div>
    </div>
  </div>
);

/**
 * Component-level loading fallback
 */
const ComponentLoadingFallback = ({ className = "" }: { className?: string }) => (
  <div className={`flex items-center justify-center p-8 ${className}`}>
    <div className="flex items-center space-x-3 animate-fade-in">
      <div className="animate-spin">
        <Brain className="h-6 w-6 text-primary" />
      </div>
      <span className="text-sm text-gray-400">Loading component...</span>
    </div>
  </div>
);

/**
 * Skeleton loader for content
 */
export const SkeletonLoader = ({ className = "" }: { className?: string }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="space-y-4">
      <div className="h-4 bg-gray-700 rounded w-3/4"></div>
      <div className="h-4 bg-gray-700 rounded w-1/2"></div>
      <div className="h-4 bg-gray-700 rounded w-5/6"></div>
    </div>
  </div>
);

/**
 * Card skeleton for project cards
 */
export const CardSkeleton = () => (
  <div className="animate-pulse">
    <div className="bg-gray-800 rounded-lg p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div className="h-6 bg-gray-700 rounded w-20"></div>
        <div className="h-4 bg-gray-700 rounded w-8"></div>
      </div>
      <div className="h-6 bg-gray-700 rounded w-3/4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-700 rounded w-full"></div>
        <div className="h-4 bg-gray-700 rounded w-2/3"></div>
      </div>
      <div className="flex gap-2">
        <div className="h-6 bg-gray-700 rounded w-16"></div>
        <div className="h-6 bg-gray-700 rounded w-20"></div>
        <div className="h-6 bg-gray-700 rounded w-14"></div>
      </div>
      <div className="h-10 bg-gray-700 rounded w-full"></div>
    </div>
  </div>
);

/**
 * Enhanced Suspense boundary with error handling and smooth transitions
 */
export const SuspenseBoundary: React.FC<SuspenseBoundaryProps> = ({
  children,
  fallback = <DefaultFallback />,
  className = "",
}) => {
  return (
    <div className={className}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </div>
  );
};

/**
 * Suspense boundary specifically for page transitions
 */
export const PageSuspenseBoundary: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  return (
    <SuspenseBoundary
      fallback={<PageLoadingFallback />}
      className="min-h-screen"
    >
      {children}
    </SuspenseBoundary>
  );
};

/**
 * Suspense boundary for lazy-loaded motion components
 */
export const MotionSuspenseBoundary: React.FC<{
  children: ReactNode;
  className?: string;
}> = ({
  children,
  className = "",
}) => {
  return (
    <SuspenseBoundary
      fallback={<ComponentLoadingFallback className={className} />}
      className={className}
    >
      {children}
    </SuspenseBoundary>
  );
};

/**
 * Suspense boundary for component sections
 */
export const SectionSuspenseBoundary: React.FC<{ 
  children: ReactNode;
  skeletonCount?: number;
}> = ({
  children,
  skeletonCount = 3,
}) => {
  return (
    <SuspenseBoundary
      fallback={
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <CardSkeleton key={index} />
          ))}
        </div>
      }
    >
      {children}
    </SuspenseBoundary>
  );
};

/**
 * Higher-order component for wrapping components with motion suspense
 */
export const withMotionSuspense = <P extends object>(
  Component: React.ComponentType<P>,
  fallbackClassName?: string
) => {
  return React.memo((props: P) => (
    <MotionSuspenseBoundary className={fallbackClassName}>
      <Component {...props} />
    </MotionSuspenseBoundary>
  ));
};

export default SuspenseBoundary;
