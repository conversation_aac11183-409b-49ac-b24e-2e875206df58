
import { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/react";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";
import { SkipLink } from "@/components/SkipLink";
import { useAccessibility } from "@/hooks/useAccessibility";
import ErrorBoundary from "@/components/ErrorBoundary";
import { PageSuspenseBoundary } from "@/components/SuspenseBoundary";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
import { MotionProvider } from "@/components/motion/MotionProvider";

// Lazy load page components for better code splitting
const Index = lazy(() => import("./pages/Index"));
const About = lazy(() => import("./pages/About"));
const Systems = lazy(() => import("./pages/Systems"));
const Contact = lazy(() => import("./pages/Contact"));
const Thanks = lazy(() => import("./pages/Thanks"));
const NotFound = lazy(() => import("./pages/NotFound"));

// Lazy load additional pages
const Projects = lazy(() => import("./pages/Projects"));
const ProjectDetail = lazy(() => import("./pages/ProjectDetail"));
const Expertise = lazy(() => import("./pages/Expertise"));

const queryClient = new QueryClient();

const AppContent = () => {
  useAccessibility();

  return (
    <MotionProvider>
      <div className="min-h-screen flex flex-col bg-black text-white">
        <SkipLink />
        <Navigation />
        <main id="main-content" className="flex-1">
          <PageSuspenseBoundary>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/about" element={<About />} />
              <Route path="/systems" element={<Systems />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/thanks" element={<Thanks />} />
              <Route path="/projects" element={<Projects />} />
              <Route path="/projects/:id" element={<ProjectDetail />} />
              <Route path="/expertise" element={<Expertise />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </PageSuspenseBoundary>
        </main>
        <Footer />
      </div>
    </MotionProvider>
  );
};

const App = () => (
  <PerformanceMonitor id="App">
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider defaultTheme="dark" storageKey="metamorphic-theme">
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <AppContent />
              </BrowserRouter>
              <Analytics />
              <SpeedInsights />
            </TooltipProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  </PerformanceMonitor>
);

export default App;
